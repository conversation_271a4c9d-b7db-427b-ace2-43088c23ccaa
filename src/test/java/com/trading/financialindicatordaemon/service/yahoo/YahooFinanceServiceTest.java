package com.trading.financialindicatordaemon.service.yahoo;

import com.trading.financialindicatordaemon.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class YahooFinanceServiceTest extends BaseTest {

    @Autowired
    private YahooFinanceService yahooFinanceService;

    public YahooFinanceServiceTest(YahooFinanceService yahooFinanceService) {
        this.yahooFinanceService = yahooFinanceService;
    }

    @Test
    public void getStockCandles_shouldReturnCandles() {
        long endTime = 1751155200;
        long startTime = 1735430400;

        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", startTime, endTime);
        assertThat(candles).isNotEmpty();
    }
}
